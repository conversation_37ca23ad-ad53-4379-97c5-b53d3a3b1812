@echo off
chcp 65001 >nul
echo 启动投票管理系统...
echo.

REM 检查Java是否安装
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Java运行环境！
    echo.
    echo 请先安装Java：
    echo 1. 访问：https://www.oracle.com/java/technologies/downloads/
    echo 2. 下载并安装Java 8或更高版本
    echo 3. 重新运行此程序
    echo.
    pause
    exit /b 1
)

REM 启动程序
java -jar "投票管理系统.jar"

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序启动失败！
    echo 请检查：
    echo 1. Java是否正确安装
    echo 2. 投票管理系统.jar文件是否存在
    echo 3. background.wav音频文件是否存在
    echo.
)

pause
