# 投票管理系统

## 项目简介

这是一个基于Java开发的投票管理系统，支持图形化界面操作，可以进行候选人设置、投票、统计和排序等功能。

## 主要功能

1. **候选人管理**
   - 支持自定义候选人（最多10个）
   - 候选人名字之间用空格分隔
   - 支持重新设置候选人

2. **投票功能**
   - 支持多次投票
   - 每张选票最多选择3个候选人
   - 自动处理废票（选择超过3人）和弃权票（未选择任何人）

3. **统计功能**
   - 实时显示总票数、有效票数、废票数、弃权票数
   - 显示每个候选人的得票数
   - 支持按得票数从高到低排序

4. **辅助功能**
   - 使用说明窗口
   - 结果保存功能
   - 背景音乐播放（可选）
   - 界面刷新重置功能

## 文件结构

```
├── VotingSystem.java          # 主要的投票系统类（Applet）
├── VotingSystemMain.java      # 独立应用程序主类
├── voting_system.html         # Applet运行页面
├── compile_and_run.bat        # 编译和运行脚本
├── README.md                  # 说明文档
└── toupiao.Java(3).java       # 原始代码文件（参考）
```

## 运行方式

### 方式一：使用批处理脚本（推荐）

1. 双击运行 `compile_and_run.bat`
2. 选择运行方式：
   - 选择1：运行独立应用程序
   - 选择2：在浏览器中运行Applet

### 方式二：手动编译运行

#### 编译代码
```bash
javac -encoding UTF-8 *.java
```

#### 运行独立应用程序
```bash
java keshe.VotingSystemMain
```

#### 运行Applet
在支持Java的浏览器中打开 `voting_system.html` 文件

## 使用说明

### 基本操作流程

1. **设置候选人**
   - 在文本框中输入候选人名单，用空格分隔
   - 点击"确认"按钮完成设置
   - 点击"取消"可以重新输入

2. **进行投票**
   - 在候选人选择框中选择要投票的候选人（最多3个）
   - 点击"确定"按钮提交选票
   - 可以重复此步骤进行多次投票

3. **查看结果**
   - 系统会实时显示统计结果
   - 点击"排序"按钮可以按得票数排序
   - 点击"保存结果"可以查看详细统计信息

4. **重新开始**
   - 点击"刷新"按钮可以重置所有数据，开始新的投票

### 投票规则

- **有效票**：选择1-3个候选人的选票
- **废票**：选择超过3个候选人的选票
- **弃权票**：没有选择任何候选人的选票

## 技术特点

1. **界面设计**
   - 使用Java AWT组件构建图形界面
   - 采用多种布局管理器（BorderLayout、GridLayout、FlowLayout）
   - 支持滚动显示和窗口缩放

2. **数据处理**
   - 使用StringTokenizer进行字符串解析
   - 实现冒泡排序算法进行结果排序
   - 实时数据统计和显示更新

3. **事件处理**
   - 实现ActionListener接口处理按钮事件
   - 支持窗口关闭事件处理
   - 多窗口管理（主窗口、帮助窗口、结果窗口）

## 系统要求

- Java Runtime Environment (JRE) 1.8 或更高版本
- 支持Java Applet的浏览器（用于Applet模式）
- Windows/Linux/macOS 操作系统

## 注意事项

1. 确保系统已安装Java运行环境
2. 如果运行Applet模式，需要浏览器支持Java插件
3. 音频文件（music.mid）为可选，如果不存在会跳过音乐播放
4. 建议使用独立应用程序模式以获得最佳体验

## 开发信息

- **开发语言**：Java
- **图形界面**：Java AWT
- **设计模式**：事件驱动编程
- **编码格式**：UTF-8

## 更新日志

### v1.0
- 实现基本的投票管理功能
- 支持候选人设置和投票统计
- 添加排序和保存功能
- 提供使用说明和帮助信息
- 支持独立应用程序和Applet两种运行模式
