@echo off
echo Starting New Voting System Interface...
echo (完全匹配图片的界面版本)
echo.

echo Compiling new version...
"D:\javaJDK8\bin\javac" -encoding UTF-8 -d . VotingSystemNew.java VotingSystemMainNew.java

if %errorlevel% equ 0 (
    echo Compilation successful!
    echo Starting GUI with green background...
    "D:\javaJDK8\bin\java" -cp . keshe.VotingSystemMainNew
) else (
    echo Compilation failed!
)

echo.
echo Press any key to exit...
pause >nul
