@echo off
title 投票管理系统启动器
color 0A

echo ========================================
echo           投票管理系统 v1.0
echo ========================================
echo.

echo [1/3] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Java运行环境！
    echo.
    echo 解决方案：
    echo 1. 下载Java: https://www.oracle.com/java/technologies/downloads/
    echo 2. 安装Java 8或更高版本
    echo 3. 重新运行此程序
    echo.
    pause
    exit /b 1
) else (
    echo [成功] Java环境检查通过
)

echo [2/3] 检查程序文件...
if not exist "投票管理系统.jar" (
    echo [错误] 未找到主程序文件！
    echo 请确保 "投票管理系统.jar" 文件在当前目录
    pause
    exit /b 1
) else (
    echo [成功] 程序文件检查通过
)

echo [3/3] 启动投票系统...
echo.
echo 正在启动程序，请稍候...
echo ========================================

REM 使用多种编码参数确保中文正常显示
java -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.language=zh -Duser.country=CN -Duser.timezone=Asia/Shanghai -jar "投票管理系统.jar"

echo.
echo ========================================
if %errorlevel% equ 0 (
    echo [信息] 程序已正常退出
) else (
    echo [错误] 程序异常退出 (错误代码: %errorlevel%)
    echo.
    echo 可能的解决方案：
    echo 1. 检查Java版本是否为8或更高
    echo 2. 确认所有文件完整
    echo 3. 检查系统权限
)
echo.
pause
