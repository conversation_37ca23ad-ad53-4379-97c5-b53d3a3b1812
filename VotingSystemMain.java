package keshe;

import java.awt.*;
import java.awt.event.*;
import javax.swing.*;

/**
 * 投票管理系统主程序
 * 可以作为独立应用程序运行，也可以作为Applet运行
 */
public class VotingSystemMain extends Frame {
    private static final long serialVersionUID = 1L;
    private VotingSystem votingSystem;
    
    public VotingSystemMain() {
        super("投票管理系统");
        
        // 创建投票系统实例
        votingSystem = new VotingSystem();
        
        // 设置窗口
        setLayout(new BorderLayout());
        add(votingSystem, BorderLayout.CENTER);
        
        // 初始化投票系统
        votingSystem.init();
        votingSystem.start();
        
        // 设置窗口属性
        setSize(800, 600);
        setLocationRelativeTo(null); // 居中显示
        
        // 添加窗口关闭事件
        addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e) {
                votingSystem.stop();
                System.exit(0);
            }
        });
        
        setVisible(true);
    }
    
    public static void main(String[] args) {
        System.out.println("启动投票管理系统...");

        // 创建和显示GUI
        new VotingSystemMain();
    }
}
