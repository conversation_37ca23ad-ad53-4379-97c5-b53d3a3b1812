package keshe;

import java.applet.*;
import java.awt.*;
import java.awt.event.*;
import java.util.StringTokenizer;
import javax.swing.JOptionPane;

/**
 * 投票管理系统 - 完全匹配图片界面
 */
public class VotingSystemNew extends Applet implements ActionListener {
    
    // 界面组件
    TextField candidate_input;
    Button confirm1, cancel, confirm2, refresh, sort, help, save;
    Label out;
    Checkbox candidate[] = new Checkbox[10];
    Label personvote[] = new Label[10];
    Panel candidatePanel, resultPanel;
    
    // 数据变量
    String candidatelist[] = new String[10];
    int count[] = {0,0,0,0,0,0,0,0,0,0};
    int totalvote = 0;
    int peoplenumber = 0;
    int count1 = 0, invalidatedTicket = 0, abstention = 0;
    
    public void init() {
        // 初始化界面组件
        candidate_input = new TextField(30);
        confirm1 = new Button("确认");
        cancel = new Button("取消");
        confirm2 = new Button("确定");
        refresh = new Button("刷新");
        sort = new Button("排序");
        help = new Button("使用说明");
        save = new Button("保存结果");
        
        out = new Label("已经统计了:0张选票，其中弃权票:0 作废票:0");
        
        // 初始状态设置
        confirm1.setEnabled(true);
        cancel.setEnabled(false);
        confirm2.setEnabled(false);
        refresh.setEnabled(false);
        sort.setEnabled(false);
        help.setEnabled(true);
        save.setEnabled(false);
        
        // 初始化候选人投票结果显示框
        for(int i = 0; i < 10; i++) {
            personvote[i] = new Label("");
            personvote[i].setVisible(false);
        }
        
        // 初始化候选人名单
        for(int i = 0; i < 10; i++) {
            candidatelist[i] = "";
        }
        
        setupLayout();
        addEventListeners();
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        // 主背景保持默认的灰白色

        // 顶部面板 - 候选人输入（正常背景）
        Panel topPanel = new Panel(new FlowLayout());
        topPanel.add(new Label("请先输入候选人姓名（人数不超过10，名字之间用空格分隔）："));
        topPanel.add(candidate_input);
        topPanel.add(confirm1);

        // 中间面板 - 只有投票区域是绿色背景
        Panel centerPanel = new Panel(new BorderLayout());

        // 投票说明 - 正常背景
        Panel noticePanel = new Panel(new FlowLayout());
        Label noticeLabel = new Label("用下面的选择框统计选票（最多选3人）：");
        noticePanel.add(noticeLabel);

        // 绿色投票区域面板
        Panel greenVotingArea = new Panel(new BorderLayout());
        greenVotingArea.setBackground(new Color(0, 200, 0)); // 只有这个区域是绿色

        // 候选人选择面板（5列布局，绿色背景）
        candidatePanel = new Panel(new GridLayout(2, 5, 20, 15));
        candidatePanel.setBackground(new Color(0, 200, 0));

        // 创建候选人选择框
        for(int i = 0; i < 10; i++) {
            candidate[i] = new Checkbox("");
            candidate[i].setBackground(new Color(0, 200, 0));
            candidate[i].setForeground(Color.BLACK);
            candidate[i].setVisible(false);
            candidatePanel.add(candidate[i]);
        }

        // 投票控制按钮面板（绿色背景）
        Panel voteButtonPanel = new Panel(new FlowLayout());
        voteButtonPanel.setBackground(new Color(0, 200, 0));
        voteButtonPanel.add(confirm2);
        voteButtonPanel.add(cancel);
        voteButtonPanel.add(sort);

        // 组装绿色投票区域
        greenVotingArea.add(candidatePanel, BorderLayout.CENTER);
        greenVotingArea.add(voteButtonPanel, BorderLayout.SOUTH);

        // 组装中间面板
        centerPanel.add(noticePanel, BorderLayout.NORTH);
        centerPanel.add(greenVotingArea, BorderLayout.CENTER);

        // 底部面板 - 结果显示（正常背景）
        Panel bottomPanel = new Panel(new BorderLayout());

        // 统计信息和控制按钮
        Panel statsPanel = new Panel(new FlowLayout(FlowLayout.LEFT));
        statsPanel.add(out);
        statsPanel.add(refresh);
        statsPanel.add(help);
        statsPanel.add(save);

        // 结果显示区域
        resultPanel = new Panel(new GridLayout(10, 1));
        for(int i = 0; i < 10; i++) {
            resultPanel.add(personvote[i]);
        }

        ScrollPane scrollPane = new ScrollPane();
        scrollPane.add(resultPanel);
        scrollPane.setPreferredSize(new Dimension(600, 120));

        bottomPanel.add(statsPanel, BorderLayout.NORTH);
        bottomPanel.add(scrollPane, BorderLayout.CENTER);

        // 组装主界面
        add(topPanel, BorderLayout.NORTH);
        add(centerPanel, BorderLayout.CENTER);
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private void addEventListeners() {
        confirm1.addActionListener(this);
        cancel.addActionListener(this);
        confirm2.addActionListener(this);
        refresh.addActionListener(this);
        sort.addActionListener(this);
        help.addActionListener(this);
        save.addActionListener(this);
    }
    
    public void actionPerformed(ActionEvent e) {
        if(e.getSource() == confirm1) {
            handleConfirmCandidates();
        } else if(e.getSource() == cancel) {
            handleCancel();
        } else if(e.getSource() == confirm2) {
            handleConfirmVote();
        } else if(e.getSource() == refresh) {
            handleRefresh();
        } else if(e.getSource() == sort) {
            handleSort();
        } else if(e.getSource() == help) {
            new Help();
        } else if(e.getSource() == save) {
            new Save();
        }
    }
    
    private void handleConfirmCandidates() {
        String input = candidate_input.getText().trim();
        if(input.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入候选人名单！");
            return;
        }
        
        StringTokenizer st = new StringTokenizer(input);
        peoplenumber = st.countTokens();
        
        if(peoplenumber > 10) {
            JOptionPane.showMessageDialog(this, "候选人数量不能超过10个！");
            return;
        }
        
        // 解析候选人名单
        st = new StringTokenizer(input);
        for(int i = 0; i < peoplenumber; i++) {
            candidatelist[i] = st.nextToken();
        }
        
        // 设置候选人选择框
        setupCandidateCheckboxes();
        
        // 更新按钮状态
        confirm1.setEnabled(false);
        cancel.setEnabled(true);
        confirm2.setEnabled(true);
        refresh.setEnabled(true);
        sort.setEnabled(true);
        save.setEnabled(true);
        candidate_input.setEnabled(false);
    }
    
    private void setupCandidateCheckboxes() {
        // 重置所有候选人选择框
        for(int i = 0; i < 10; i++) {
            candidate[i].setVisible(false);
            candidate[i].setState(false);
        }
        
        // 显示当前候选人
        for(int i = 0; i < peoplenumber; i++) {
            candidate[i].setLabel(candidatelist[i]);
            candidate[i].setVisible(true);
        }
        
        repaint();
    }

    private void handleCancel() {
        // 重置所有选择框
        for(int i = 0; i < peoplenumber; i++) {
            candidate[i].setState(false);
        }
    }

    private void handleConfirmVote() {
        // 统计选了多少人
        count1 = 0;
        for(int i = 0; i < peoplenumber; i++) {
            if(candidate[i].getState()) {
                count1++;
            }
        }

        totalvote++;

        if(count1 == 0) {
            abstention++; // 弃权票
        } else if(count1 > 3) {
            invalidatedTicket++; // 废票
        } else {
            // 有效票，统计各候选人得票数
            for(int i = 0; i < peoplenumber; i++) {
                if(candidate[i].getState()) {
                    count[i]++;
                }
            }
        }

        // 清空选择框
        for(int i = 0; i < peoplenumber; i++) {
            candidate[i].setState(false);
        }

        updateResults();
    }

    private void handleRefresh() {
        // 重置所有状态
        confirm1.setEnabled(true);
        cancel.setEnabled(false);
        confirm2.setEnabled(false);
        refresh.setEnabled(false);
        sort.setEnabled(false);
        save.setEnabled(false);
        candidate_input.setEnabled(true);
        candidate_input.setText("");

        // 重置候选人数据
        peoplenumber = 0;
        totalvote = 0;
        count1 = 0;
        invalidatedTicket = 0;
        abstention = 0;

        for(int i = 0; i < 10; i++) {
            candidatelist[i] = "";
            count[i] = 0;
            candidate[i].setVisible(false);
            candidate[i].setState(false);
            personvote[i].setText("");
            personvote[i].setVisible(false);
        }

        out.setText("已经统计了:0张选票，其中弃权票:0 作废票:0");
        repaint();
    }

    private void handleSort() {
        // 冒泡排序，按得票数从高到低排序
        for(int i = 0; i < peoplenumber - 1; i++) {
            for(int j = i + 1; j < peoplenumber; j++) {
                if(count[i] < count[j]) {
                    // 交换得票数
                    int tempCount = count[i];
                    count[i] = count[j];
                    count[j] = tempCount;

                    // 交换候选人名字
                    String tempName = candidatelist[i];
                    candidatelist[i] = candidatelist[j];
                    candidatelist[j] = tempName;
                }
            }
        }

        updateResults();
    }

    private void updateResults() {
        out.setText("已经统计了:" + totalvote + "张选票，其中弃权票:" + abstention + " 作废票:" + invalidatedTicket);

        // 更新各候选人得票数显示
        for(int j = 0; j < peoplenumber; j++) {
            personvote[j].setText(candidatelist[j] + " 得票数:" + count[j]);
            personvote[j].setVisible(true);
        }

        // 隐藏未使用的标签
        for(int j = peoplenumber; j < 10; j++) {
            personvote[j].setVisible(false);
        }

        repaint();
    }

    // 使用说明窗口类
    class Help extends Frame {
        Label help[] = new Label[6];

        Help() {
            setTitle("使用说明");
            setSize(500, 300);
            setLayout(new GridLayout(6, 1));

            for(int i = 0; i < 6; i++) {
                help[i] = new Label();
                add(help[i]);
            }

            help[0].setText("投票管理系统使用说明：");
            help[1].setText("1: 在文本框中输入候选人名单，点击\"确认\"以完成候选人的设置，点击\"取消\"可以重新设置候选人。");
            help[2].setText("2: 对候选人进行投票，点击下面的\"确定\"以确认选票。(注意：每点一次确定将产生一张选票！)");
            help[3].setText("3: 确定选票后，会自动统计结果，点击\"排序\"可以对候选人所得的票数由高到低进行排序。");
            help[4].setText("4: 点击\"刷新\"可以重新设置候选人，并开始新的一轮投票");
            help[5].setText("5: 在任何时候可以点击\"使用说明\"来查看帮助，点击\"保存结果\"，可以将统计以文本的形式显示出来。");

            addWindowListener(new CloseWin());
            setVisible(true);
        }

        class CloseWin extends WindowAdapter {
            public void windowClosing(WindowEvent e) {
                setVisible(false);
            }
        }
    }

    // 保存结果窗口类
    class Save extends Frame {
        TextArea result;

        Save() {
            setTitle("投票结果");
            setSize(400, 300);
            setLayout(new BorderLayout());

            result = new TextArea();
            result.setEditable(false);

            StringBuilder sb = new StringBuilder();
            sb.append("投票统计结果\n");
            sb.append("===================\n");
            sb.append("\n总票数: ").append(totalvote);
            sb.append("\n有效票: ").append(totalvote - invalidatedTicket - abstention);
            sb.append("\n废票: ").append(invalidatedTicket);
            sb.append("\n弃权票: ").append(abstention);
            sb.append("\n\n候选人得票情况:\n");

            for(int i = 0; i < peoplenumber; i++) {
                sb.append((i+1)).append(". ").append(candidatelist[i]).append(": ").append(count[i]).append("票\n");
            }

            result.setText(sb.toString());
            add(result, BorderLayout.CENTER);

            addWindowListener(new CloseWin());
            setVisible(true);
        }

        class CloseWin extends WindowAdapter {
            public void windowClosing(WindowEvent e) {
                setVisible(false);
            }
        }
    }
}
