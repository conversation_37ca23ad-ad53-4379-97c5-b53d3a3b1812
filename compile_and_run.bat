@echo off
echo 投票管理系统编译和运行脚本
echo ================================

echo 正在编译Java文件...
javac -cp . *.java

if %errorlevel% neq 0 (
    echo 编译失败！请检查Java代码。
    pause
    exit /b 1
)

echo 编译成功！

echo.
echo 选择运行方式：
echo 1. 运行功能测试
echo 2. 运行独立应用程序
echo 3. 在浏览器中运行Applet
echo 4. 退出

set /p choice=请输入选择 (1-4):

if "%choice%"=="1" (
    echo 正在运行功能测试...
    java keshe.TestVotingSystem
) else if "%choice%"=="2" (
    echo 正在启动独立应用程序...
    java keshe.VotingSystemMain
) else if "%choice%"=="3" (
    echo 正在打开浏览器运行Applet...
    start voting_system.html
) else if "%choice%"=="4" (
    echo 退出程序。
) else (
    echo 无效选择，退出程序。
)

pause
