package keshe;

import java.io.*;
import java.awt.*;
import java.util.*;
import java.applet.*;
import java.awt.event.*;
import javax.swing.*; 

public class VotingSystem extends Applet implements ActionListener{
    private static final long serialVersionUID = 1L;
    AudioClip music;// 播放音乐
    Label hint,result,notice,banquan;
    TextField candidate_input;// 输入候选人文本框
    TextField out;// 显示选举结果的文本框
    Button confirm1,cancel,confirm2,refresh,sort;// 分别表示确认、取消、确定、刷新、排序
    Button help;// 使用说明
    Button save;// 保存统计结果
    Checkbox candidate[]=new Checkbox[10];// 选择框数组，代表候选人
    TextField personvote[]=new TextField[10];// 文本条数组，显示每个人的得票情况
    String candidatelist[]=new String[10];// 候选人名单
    int count[]={0,0,0,0,0,0,0,0,0,0};// 记录每个人的得票数
    int totalvote=0;// 总票数
    int peoplenumber=0;// 候选人个数
    int count1=0,invalidatedTicket=0,abstention=0; // 分别表示选的人数，废票数，弃权票数
    
    public void init(){
        // 初始化音乐（如果音频文件存在的话）
        try {
            music=getAudioClip(getCodeBase(),"music.mid");
        } catch (Exception e) {
            System.out.println("音频文件未找到，跳过音乐播放");
        }
        
        // 初始化界面组件
        hint=new Label("首先输入候选人的名字（人数不超过10，名字之间用空格分隔）:");
        notice=new Label("用下面的选择框统计选票(最多选3人):");
        result=new Label("选举结果:");
        banquan=new Label("投票管理系统 v1.0");
        
        candidate_input=new TextField(50);
        confirm1=new Button("确认");
        cancel=new Button("取消");
        confirm2=new Button("确定");
        refresh=new Button("刷新");
        sort=new Button("排序");
        
        // 初始状态设置
        confirm2.setEnabled(false);
        refresh.setEnabled(false);
        sort.setEnabled(false);
        
        help=new Button("使用说明");
        save=new Button("保存结果");
        save.setEnabled(false);
        
        out=new TextField(50);
        out.setEditable(false);
        
        // 初始化候选人投票结果显示框
        for(int i=0;i<10;i++) {
            personvote[i]=new TextField(80);
            personvote[i].setEditable(false);
        }
        
        // 初始化候选人名单
        for(int i=0;i<10;i++) {
            candidatelist[i]="";
        }
        
        setupLayout();
        addEventListeners();
    }
    
    private void setupLayout() {
        // 创建面板
        Panel p=new Panel();
        Panel p1=new Panel();
        Panel p2=new Panel();
        Panel p3=new Panel();
        Panel p4=new Panel();
        Panel p5=new Panel();
        Panel p6=new Panel();
        Panel p7=new Panel();
        Panel pa=new Panel();
        Panel pb=new Panel();
        Panel pc=new Panel();
        
        // 设置布局
        setLayout(new BorderLayout());
        pa.setLayout(new GridLayout(7,1));
        pb.setLayout(new BorderLayout());
        p4.setLayout(new GridLayout(1,5));
        p5.setLayout(new GridLayout(1,5));
        
        // 添加组件到面板
        p1.add(hint);
        p2.add(candidate_input);
        p2.add(help);
        p3.add(confirm1);
        p3.add(cancel);
        p3.add(notice);
        
        // 设置背景颜色
        p4.setBackground(Color.lightGray);
        p5.setBackground(Color.lightGray);
        p6.setBackground(Color.lightGray);
        
        // 创建候选人选择框
        for(int i=0;i<5;i++){
            candidate[i]=new Checkbox("");
            candidate[i].setEnabled(false);
            p4.add(candidate[i]);
        }
        for(int i=5;i<10;i++){
            candidate[i]=new Checkbox("");
            candidate[i].setEnabled(false);
            p5.add(candidate[i]);
        }
        
        p6.add(confirm2);
        p6.add(refresh);
        p6.add(sort);
        p7.add(result);
        p7.add(out);
        p7.add(save);
        
        // 组装主面板
        pa.add(p1);
        pa.add(p2);
        pa.add(p3);
        pa.add(p4);
        pa.add(p5);
        pa.add(p6);
        pa.add(p7);
        
        // 创建结果显示区域
        p.setLayout(new GridLayout(10,1));
        for(int i=0;i<10;i++){ 
            p.add(personvote[i]);
        }
        
        ScrollPane scroll=new ScrollPane();
        scroll.add(p);
        pc.add(banquan);
        pb.add("Center",scroll);
        pb.add("South",pc);
        
        add("Center",pa);
        add("South",pb);
    }
    
    private void addEventListeners() {
        confirm1.addActionListener(this);
        cancel.addActionListener(this);
        confirm2.addActionListener(this);
        refresh.addActionListener(this);
        sort.addActionListener(this);
        help.addActionListener(this);
        save.addActionListener(this);
    }
    
    public void start(){
        // 循环播放音乐
        if(music != null) {
            music.loop();
        }
    }
    
    public void stop(){
        // 结束播放
        if(music != null) {
            music.stop();
        }
    }
    
    public void actionPerformed(ActionEvent e){
        String s=e.getActionCommand();
        
        if(s.equals("确认")){
            handleConfirmCandidates();
        }
        else if(s.equals("取消")){
            handleCancelInput();
        }
        else if(s.equals("确定")){
            handleConfirmVote();
        }
        else if(s.equals("刷新")){
            handleRefresh();
        }
        else if(s.equals("排序")){
            handleSort();
        }
        else if(s.equals("使用说明")){
            new Help();
        }
        else if(s.equals("保存结果")){
            new Save();
        }
    }
    
    private void handleConfirmCandidates() {
        confirm1.setEnabled(false);
        save.setEnabled(true);
        confirm2.setEnabled(true);
        refresh.setEnabled(true);
        sort.setEnabled(true);
        help.setEnabled(true);
        
        String input = candidate_input.getText().trim();
        if(input.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入候选人名单！");
            confirm1.setEnabled(true);
            return;
        }
        
        StringTokenizer st = new StringTokenizer(input);
        peoplenumber = st.countTokens();
        
        if(peoplenumber > 10) {
            JOptionPane.showMessageDialog(this, "候选人数量不能超过10个！");
            confirm1.setEnabled(true);
            return;
        }
        
        int i = 0;
        while(st.hasMoreTokens() && i < 10){
            candidatelist[i] = st.nextToken();
            i++;
        }
        
        // 设置候选人选择框
        for(int j=0;j<10;j++) {
            if(j < peoplenumber) {
                candidate[j].setLabel(candidatelist[j]);
                candidate[j].setEnabled(true);
                candidate[j].setVisible(true);
            } else {
                candidate[j].setVisible(false);
            }
        }
        
        updateDisplay();
    }
    
    private void handleCancelInput() {
        confirm1.setEnabled(true);
        candidate_input.setText("");
        // 重置所有候选人选择框
        for(int j=0;j<10;j++) {
            candidate[j].setEnabled(false);
            candidate[j].setVisible(true);
            candidate[j].setLabel("");
        }
    }
    
    private void handleConfirmVote() {
        totalvote++;
        count1 = 0;
        sort.setEnabled(true);
        
        // 统计选了多少人
        for(int j=0;j<peoplenumber;j++){
            if(candidate[j].getState()) {
                count1++;
            }
        }
        
        // 处理投票逻辑
        if(count1 == 0) {
            abstention++; // 弃权票
        } else if(count1 > 3) {
            invalidatedTicket++; // 废票
        } else {
            // 有效票，统计各候选人得票数
            for(int j=0;j<peoplenumber;j++) {
                if(candidate[j].getState()) {
                    count[j]++;
                }
            }
        }
        
        // 清空选择框
        for(int j=0;j<10;j++) {
            candidate[j].setState(false);
        }
        
        updateDisplay();
    }
    
    private void handleRefresh() {
        // 重置所有状态
        confirm1.setEnabled(true);
        confirm2.setEnabled(false);
        refresh.setEnabled(false);
        sort.setEnabled(false);
        save.setEnabled(false);
        
        totalvote = 0;
        peoplenumber = 0;
        count1 = 0;
        invalidatedTicket = 0;
        abstention = 0;
        
        candidate_input.setText("");
        out.setText("");
        
        // 重置候选人数据
        for(int j=0;j<10;j++){
            candidate[j].setState(false);
            candidate[j].setVisible(true);
            candidate[j].setEnabled(false);
            candidate[j].setLabel("");
            candidatelist[j] = "";
            count[j] = 0;
            personvote[j].setText("");
        }
    }
    
    private void handleSort() {
        sort.setEnabled(false);
        
        // 冒泡排序，按得票数从高到低排序
        for(int j=0;j<peoplenumber-1;j++) {
            for(int i=j+1;i<peoplenumber;i++) {
                if(count[j] < count[i]){
                    // 交换得票数
                    int tempCount = count[j];
                    count[j] = count[i];
                    count[i] = tempCount;
                    
                    // 交换候选人名字
                    String tempName = candidatelist[j];
                    candidatelist[j] = candidatelist[i];
                    candidatelist[i] = tempName;
                }
            }
        }
        
        updateDisplay();
    }
    
    private void updateDisplay() {
        // 更新统计结果显示
        out.setText("已经统计了:" + totalvote + "张选票，其中弃权票:" + abstention + " 作废票:" + invalidatedTicket);

        // 更新各候选人得票数显示
        for(int j=0;j<peoplenumber;j++) {
            personvote[j].setText(candidatelist[j] + " 得票数:" + count[j]);
        }

        // 清空多余的显示框
        for(int j=peoplenumber;j<10;j++) {
            personvote[j].setText("");
        }
    }

    // 使用说明窗口类
    class Help extends Frame {
        private static final long serialVersionUID = 1L;
        Panel p = new Panel();
        TextField help[] = new TextField[6];

        Help(){
            super("使用说明");
            p.setLayout(new GridLayout(6,1));

            for(int i=0;i<6;i++) {
                help[i] = new TextField(80);
                help[i].setEditable(false);
                p.add(help[i]);
            }

            ScrollPane scroll = new ScrollPane();
            scroll.add(p);
            add(scroll);

            help[0].setText("使用说明:");
            help[1].setText("1: 在文本框中输入候选人名单，点击\"确认\"以完成候选人的设置，点击\"取消\"可以重新设置候选人。");
            help[2].setText("2: 对候选人进行投票，点击下面的\"确定\"以确认选票。(注意：每点一次确定将产生一张选票！)");
            help[3].setText("3: 确定选票后，会自动统计结果，点击\"排序\"可以对候选人所得的票数由高到低进行排序。");
            help[4].setText("4: 点击\"刷新\"可以重新设置候选人，并开始新的一轮投票");
            help[5].setText("5: 在任何时候可以点击\"使用说明\"来查看帮助，点击\"保存结果\"，可以将统计以文本的形式显示出来。");

            setSize(700,250);
            setVisible(true);
            addWindowListener(new CloseWin());
        }

        class CloseWin extends WindowAdapter{
            public void windowClosing(WindowEvent e){
                Window w = e.getWindow();
                w.dispose();
            }
        }
    }

    // 保存结果窗口类
    class Save extends Frame {
        private static final long serialVersionUID = 1L;
        TextArea save;

        Save(){
            super("统计结果");
            save = new TextArea(15,50);
            save.setEditable(false);
            add(save);

            StringBuilder result = new StringBuilder();
            result.append(out.getText()).append("\n");
            result.append("详细结果:\n");

            for(int i=0;i<peoplenumber;i++) {
                if(!personvote[i].getText().isEmpty()) {
                    result.append(personvote[i].getText()).append("\n");
                }
            }

            result.append("\n有效票数: ").append(totalvote - invalidatedTicket - abstention);
            result.append("\n总票数: ").append(totalvote);

            save.setText(result.toString());

            setSize(400,350);
            setVisible(true);
            addWindowListener(new CloseWin());
        }

        class CloseWin extends WindowAdapter{
            public void windowClosing(WindowEvent e){
                Window w = e.getWindow();
                w.dispose();
            }
        }
    }
}
