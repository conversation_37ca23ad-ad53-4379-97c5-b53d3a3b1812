# 投票管理系统 v1.0

## 📋 项目简介
这是一个基于Java Swing开发的投票管理系统，支持候选人管理、实时投票、结果统计等功能。

## 🚀 快速开始

### 运行要求
- Java 8 或更高版本
- Windows操作系统

### 启动方法
1. 确保已安装Java运行环境
2. 双击 `启动投票系统.bat` 文件
3. 系统将自动启动并播放背景音乐

## ✨ 功能特色

### 核心功能
- ✅ 支持最多10个候选人
- ✅ 实时投票统计
- ✅ 投票结果排序
- ✅ 结果保存到文件
- ✅ 帮助说明功能

### 界面特色
- 🎨 绿色主题界面设计
- 🎵 背景音乐播放
- 📱 友好的用户界面
- 🔄 实时数据更新

## 📖 使用说明

### 添加候选人
1. 在顶部输入框中输入候选人姓名
2. 多个候选人用空格分隔
3. 点击"确认"按钮添加

### 进行投票
1. 在候选人列表中选择要投票的候选人（可多选）
2. 点击"确定"按钮提交投票
3. 系统会实时更新票数

### 查看结果
1. 投票结果显示在底部区域
2. 点击"排序"按钮按票数排序
3. 点击"保存结果"可保存到文件

### 其他功能
- **刷新**：清空当前选择
- **帮助**：查看详细使用说明
- **使用说明**：查看系统信息

## 🎵 音频说明
- 系统启动时会自动播放背景音乐
- 音频文件：`background.wav`
- 如无声音，请检查音频文件是否存在

## 📁 文件结构
```
投票管理系统发布包/
├── 投票管理系统.jar          # 主程序文件
├── 启动投票系统.bat          # 启动脚本
├── background.wav           # 背景音乐文件
├── 使用说明.txt            # 使用说明
├── README.md               # 项目说明
└── 源代码/                 # 源代码文件
    ├── VotingSystem.java
    └── VotingSystemMain.java
```

## 🔧 故障排除

### 程序无法启动
1. 检查Java是否正确安装
2. 确认Java版本为8或更高
3. 检查JAR文件是否完整

### 没有背景音乐
1. 确认 `background.wav` 文件存在
2. 检查音频文件是否损坏
3. 确认系统音量设置

### 中文显示异常
1. 确认系统支持UTF-8编码
2. 检查Java字体设置

## 📞 技术支持
如遇到问题，请检查：
1. Java环境是否正确安装
2. 所有文件是否完整
3. 系统权限是否足够

Java下载地址：https://www.oracle.com/java/technologies/downloads/

## 📄 版本信息
- 版本：v1.0
- 开发语言：Java
- 界面框架：Swing/AWT
- 兼容性：Windows + Java 8+
