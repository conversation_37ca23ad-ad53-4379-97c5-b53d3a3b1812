@echo off
echo Starting Voting System...
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Java not found! Please install Java first.
    echo Download from: https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
)

REM Start the program with UTF-8 encoding
java -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -jar "投票管理系统.jar"

if %errorlevel% neq 0 (
    echo.
    echo Program failed to start!
    echo Please check:
    echo 1. Java is properly installed
    echo 2. JAR file exists
    echo 3. Audio file exists
    echo.
)

pause
