投票管理系统 v1.0
===================

运行要求：
- Java 8 或更高版本

启动方法：
双击 "启动投票系统.bat" 文件

功能特色：
- 支持最多10个候选人
- 实时投票统计和排序
- 背景音乐播放
- 投票结果保存功能
- 绿色界面设计

使用说明：
1. 在输入框中输入候选人姓名（用空格分隔）
2. 点击"确认"按钮添加候选人
3. 选择要投票的候选人（可多选）
4. 点击"确定"按钮进行投票
5. 使用"刷新"按钮清空选择
6. 使用"排序"按钮按票数排序
7. 可以保存投票结果到文件

注意事项：
- 如果没有背景音乐，请确保 background.wav 文件在同一目录下
- 如果程序无法启动，请检查Java是否正确安装
- 支持中文候选人姓名

技术支持：
如有问题，请检查Java环境是否正确安装
下载Java：https://www.oracle.com/java/technologies/downloads/
