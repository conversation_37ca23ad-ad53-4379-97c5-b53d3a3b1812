投票系统背景音乐设置说明
============================

支持的音频格式：
1. WAV格式 (推荐) - background.wav
2. AU格式 - background.au  
3. MIDI格式 - music.mid

设置步骤：
=========

方法一：使用WAV格式 (推荐)
------------------------
1. 将你的MP3文件转换为WAV格式
   - 可以使用在线转换工具：https://convertio.co/mp3-wav/
   - 或使用软件如Audacity、格式工厂等
   
2. 将转换后的WAV文件重命名为 "background.wav"

3. 将 background.wav 文件放在以下位置：
   - 与VotingSystem.java同一目录
   - 或与编译后的.class文件同一目录

方法二：使用在线转换
------------------
1. 访问 https://convertio.co/mp3-wav/
2. 上传你的MP3文件
3. 选择转换为WAV格式
4. 下载转换后的文件
5. 重命名为 background.wav

方法三：使用Audacity (免费软件)
-----------------------------
1. 下载安装Audacity: https://www.audacityteam.org/
2. 打开Audacity，导入你的MP3文件
3. 选择 文件 -> 导出 -> 导出为WAV
4. 保存为 background.wav

音频文件放置位置：
================
请将音频文件放在项目根目录下：
c:\Users\<USER>\Desktop\yunx\background.wav

测试音频：
=========
重新编译并运行程序，如果音频文件正确放置，
控制台会显示："成功加载 background.wav"

注意事项：
=========
1. 音频文件不要太大，建议小于10MB
2. 推荐使用WAV格式以获得最佳兼容性
3. 如果不需要背景音乐，程序会自动跳过音频播放
4. 音频会在程序启动时循环播放
