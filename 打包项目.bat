@echo off
chcp 65001 >nul
echo 投票管理系统打包工具
echo ====================
echo.

echo 第1步：清理旧文件...
if exist "投票管理系统.jar" del "投票管理系统.jar"
if exist "投票管理系统发布包" rmdir /s /q "投票管理系统发布包"

echo 第2步：重新编译项目...
"D:\javaJDK8\bin\javac" -encoding UTF-8 -d . VotingSystem.java VotingSystemMain.java
if %errorlevel% neq 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)
echo ✅ 编译成功

echo 第3步：创建JAR文件...
"D:\javaJDK8\bin\jar" cfm "投票管理系统.jar" MANIFEST.MF keshe/*.class background.wav
if %errorlevel% neq 0 (
    echo ❌ JAR创建失败！
    pause
    exit /b 1
)
echo ✅ JAR文件创建成功

echo 第4步：创建发布包目录...
mkdir "投票管理系统发布包"
mkdir "投票管理系统发布包\音频文件"
mkdir "投票管理系统发布包\源代码"
mkdir "投票管理系统发布包\文档"

echo 第5步：复制文件到发布包...
copy "投票管理系统.jar" "投票管理系统发布包\"
copy "background.wav" "投票管理系统发布包\"
copy "zhoumusic.mp3" "投票管理系统发布包\音频文件\"
copy "zhoumusic.wav" "投票管理系统发布包\音频文件\"
copy "VotingSystem.java" "投票管理系统发布包\源代码\"
copy "VotingSystemMain.java" "投票管理系统发布包\源代码\"
copy "投票说明书杨沛欣、杨欣雨、杨雪.pdf" "投票管理系统发布包\文档\" 2>nul
copy "项目总结.md" "投票管理系统发布包\文档\" 2>nul
copy "快速音频设置.md" "投票管理系统发布包\文档\" 2>nul

echo 第6步：创建运行脚本...
echo @echo off > "投票管理系统发布包\启动投票系统.bat"
echo chcp 65001 ^>nul >> "投票管理系统发布包\启动投票系统.bat"
echo echo 启动投票管理系统... >> "投票管理系统发布包\启动投票系统.bat"
echo echo. >> "投票管理系统发布包\启动投票系统.bat"
echo java -jar "投票管理系统.jar" >> "投票管理系统发布包\启动投票系统.bat"
echo if %%errorlevel%% neq 0 ( >> "投票管理系统发布包\启动投票系统.bat"
echo     echo. >> "投票管理系统发布包\启动投票系统.bat"
echo     echo 启动失败！请确保已安装Java运行环境 >> "投票管理系统发布包\启动投票系统.bat"
echo     echo 下载Java：https://www.oracle.com/java/technologies/downloads/ >> "投票管理系统发布包\启动投票系统.bat"
echo     echo. >> "投票管理系统发布包\启动投票系统.bat"
echo ) >> "投票管理系统发布包\启动投票系统.bat"
echo pause >> "投票管理系统发布包\启动投票系统.bat"

echo 第7步：创建说明文件...
echo 投票管理系统 v1.0 > "投票管理系统发布包\使用说明.txt"
echo =================== >> "投票管理系统发布包\使用说明.txt"
echo. >> "投票管理系统发布包\使用说明.txt"
echo 运行要求： >> "投票管理系统发布包\使用说明.txt"
echo - Java 8 或更高版本 >> "投票管理系统发布包\使用说明.txt"
echo. >> "投票管理系统发布包\使用说明.txt"
echo 启动方法： >> "投票管理系统发布包\使用说明.txt"
echo 双击 "启动投票系统.bat" 文件 >> "投票管理系统发布包\使用说明.txt"
echo. >> "投票管理系统发布包\使用说明.txt"
echo 功能特色： >> "投票管理系统发布包\使用说明.txt"
echo - 支持最多10个候选人 >> "投票管理系统发布包\使用说明.txt"
echo - 实时投票统计和排序 >> "投票管理系统发布包\使用说明.txt"
echo - 背景音乐播放 >> "投票管理系统发布包\使用说明.txt"
echo - 投票结果保存功能 >> "投票管理系统发布包\使用说明.txt"
echo - 绿色界面设计 >> "投票管理系统发布包\使用说明.txt"
echo. >> "投票管理系统发布包\使用说明.txt"
echo 如果没有声音，请确保 background.wav 文件在同一目录下 >> "投票管理系统发布包\使用说明.txt"

echo.
echo ✅ 打包完成！
echo.
echo 📦 发布包位置：投票管理系统发布包\
echo 📋 包含文件：
echo    - 投票管理系统.jar (主程序)
echo    - 启动投票系统.bat (启动脚本)
echo    - background.wav (背景音乐)
echo    - 使用说明.txt (使用说明)
echo    - 源代码\ (源代码文件)
echo    - 音频文件\ (原始音频文件)
echo    - 文档\ (项目文档)
echo.
echo 🚀 现在可以将"投票管理系统发布包"文件夹复制到其他电脑使用！
echo.
pause
