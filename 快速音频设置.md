# 投票系统背景音乐快速设置

## 🎵 最简单的方法

### 步骤1：在线转换MP3到WAV
1. 打开浏览器，访问：https://convertio.co/mp3-wav/
2. 点击"选择文件"，上传你的MP3音乐文件
3. 点击"转换"按钮
4. 下载转换后的WAV文件

### 步骤2：重命名和放置
1. 将下载的WAV文件重命名为：`background.wav`
2. 将文件放在项目目录：`c:\Users\<USER>\Desktop\yunx\background.wav`

### 步骤3：测试
1. 双击 `start.bat` 启动程序
2. 如果设置成功，控制台会显示："成功加载 background.wav"

## 🎼 支持的音频格式

程序会按以下顺序尝试加载音频文件：
1. `background.wav` (推荐)
2. `background.au`
3. `music.mid`

## 🔧 其他转换方法

### 使用Audacity (免费软件)
1. 下载：https://www.audacityteam.org/
2. 安装后打开，导入MP3文件
3. 选择：文件 → 导出 → 导出为WAV
4. 保存为 `background.wav`

### 使用格式工厂
1. 下载安装格式工厂
2. 选择音频转换 → WAV
3. 添加MP3文件，转换为WAV
4. 重命名为 `background.wav`

## ⚠️ 注意事项

- 音频文件建议小于10MB
- WAV格式兼容性最好
- 如果没有音频文件，程序正常运行，只是没有背景音乐
- 音频会在程序启动时自动循环播放

## 🎯 推荐的背景音乐

建议选择：
- 轻松的器乐音乐
- 音量适中的音乐
- 时长2-5分钟的音乐（会自动循环）
- 避免有歌词的音乐（可能分散注意力）
