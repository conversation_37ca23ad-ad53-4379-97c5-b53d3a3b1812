package keshe;

import java.awt.*;
import java.awt.event.*;

/**
 * 投票管理系统主程序 - 新版本
 * 完全匹配图片界面
 */
public class VotingSystemMainNew extends Frame {
    private VotingSystemNew votingSystem;
    
    public VotingSystemMainNew() {
        // 初始化投票系统
        votingSystem = new VotingSystemNew();
        votingSystem.init();
        
        // 设置窗口属性
        setTitle("小型投票系统测试 - vote.VoteTest.class");
        setSize(700, 500);
        setLocationRelativeTo(null);
        
        // 添加投票系统到窗口
        add(votingSystem);
        
        // 添加窗口关闭事件
        addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e) {
                System.exit(0);
            }
        });
        
        // 显示窗口
        setVisible(true);
        
        System.out.println("投票管理系统启动成功！");
    }
    
    public static void main(String[] args) {
        System.out.println("启动新版投票管理系统...");
        
        // 创建和显示GUI
        new VotingSystemMainNew();
    }
}
