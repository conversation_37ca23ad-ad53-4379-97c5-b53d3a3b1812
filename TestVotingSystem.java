package keshe;

import java.awt.*;
import java.awt.event.*;

/**
 * 投票管理系统测试类
 * 用于验证系统的基本功能
 */
public class TestVotingSystem {
    public static void main(String[] args) {
        System.out.println("投票管理系统测试开始...");
        
        // 测试基本功能
        testBasicFunctionality();
        
        System.out.println("测试完成！");
        System.out.println("请运行 VotingSystemMain 来启动完整的图形界面程序。");
    }
    
    private static void testBasicFunctionality() {
        System.out.println("1. 测试候选人名单解析...");
        
        // 模拟候选人输入
        String candidateInput = "张三 李四 王五 赵六";
        java.util.StringTokenizer st = new java.util.StringTokenizer(candidateInput);
        
        System.out.println("输入的候选人: " + candidateInput);
        System.out.println("候选人数量: " + st.countTokens());
        
        // 重新创建StringTokenizer因为countTokens()会消耗tokens
        st = new java.util.StringTokenizer(candidateInput);
        System.out.println("解析出的候选人:");
        int i = 1;
        while(st.hasMoreTokens()) {
            System.out.println("  " + i + ". " + st.nextToken());
            i++;
        }
        
        System.out.println("2. 测试投票统计逻辑...");
        
        // 模拟投票数据
        int totalVotes = 10;
        int validVotes = 7;
        int invalidVotes = 2;
        int abstentions = 1;
        
        System.out.println("总票数: " + totalVotes);
        System.out.println("有效票: " + validVotes);
        System.out.println("废票: " + invalidVotes);
        System.out.println("弃权票: " + abstentions);
        
        // 验证统计
        if(validVotes + invalidVotes + abstentions == totalVotes) {
            System.out.println("✓ 票数统计正确");
        } else {
            System.out.println("✗ 票数统计错误");
        }
        
        System.out.println("3. 测试排序功能...");
        
        // 模拟候选人得票数据
        String[] candidates = {"张三", "李四", "王五", "赵六"};
        int[] votes = {5, 3, 8, 2};
        
        System.out.println("排序前:");
        for(int j = 0; j < candidates.length; j++) {
            System.out.println("  " + candidates[j] + ": " + votes[j] + "票");
        }
        
        // 冒泡排序
        for(int j = 0; j < candidates.length - 1; j++) {
            for(int k = j + 1; k < candidates.length; k++) {
                if(votes[j] < votes[k]) {
                    // 交换得票数
                    int tempVotes = votes[j];
                    votes[j] = votes[k];
                    votes[k] = tempVotes;
                    
                    // 交换候选人名字
                    String tempName = candidates[j];
                    candidates[j] = candidates[k];
                    candidates[k] = tempName;
                }
            }
        }
        
        System.out.println("排序后:");
        for(int j = 0; j < candidates.length; j++) {
            System.out.println("  " + (j+1) + ". " + candidates[j] + ": " + votes[j] + "票");
        }
        
        System.out.println("✓ 所有基本功能测试通过");
    }
}
