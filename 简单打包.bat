@echo off
echo 投票管理系统打包工具
echo ====================
echo.

echo 第1步：重新编译...
"D:\javaJDK8\bin\javac" -encoding UTF-8 -d . VotingSystem.java VotingSystemMain.java
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)
echo 编译成功

echo 第2步：创建JAR文件...
"D:\javaJDK8\bin\jar" cfm "投票管理系统.jar" MANIFEST.MF keshe/*.class background.wav
if %errorlevel% neq 0 (
    echo JAR创建失败！
    pause
    exit /b 1
)
echo JAR文件创建成功

echo 第3步：创建发布包...
if exist "投票管理系统发布包" rmdir /s /q "投票管理系统发布包"
mkdir "投票管理系统发布包"

echo 第4步：复制文件...
copy "投票管理系统.jar" "投票管理系统发布包\"
copy "background.wav" "投票管理系统发布包\"
copy "启动投票系统.bat" "投票管理系统发布包\"

echo 第5步：创建说明文件...
echo 投票管理系统 v1.0 > "投票管理系统发布包\使用说明.txt"
echo =================== >> "投票管理系统发布包\使用说明.txt"
echo. >> "投票管理系统发布包\使用说明.txt"
echo 运行要求：Java 8 或更高版本 >> "投票管理系统发布包\使用说明.txt"
echo 启动方法：双击 "启动投票系统.bat" >> "投票管理系统发布包\使用说明.txt"
echo. >> "投票管理系统发布包\使用说明.txt"
echo 功能特色： >> "投票管理系统发布包\使用说明.txt"
echo - 支持最多10个候选人 >> "投票管理系统发布包\使用说明.txt"
echo - 实时投票统计和排序 >> "投票管理系统发布包\使用说明.txt"
echo - 背景音乐播放 >> "投票管理系统发布包\使用说明.txt"
echo - 投票结果保存功能 >> "投票管理系统发布包\使用说明.txt"
echo - 绿色界面设计 >> "投票管理系统发布包\使用说明.txt"

echo.
echo 打包完成！
echo 发布包位置：投票管理系统发布包\
echo.
echo 现在可以将整个文件夹复制到其他电脑使用！
echo.
pause
