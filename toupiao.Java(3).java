package keshe;

import java.io.*;
import java.awt.*;
import java.util.*;
import java.applet.*;
import java.awt.event.*;
import javax.swing.*;

public class Nice extends Applet implements ActionListener{
    private static final long serialVersionUID = 1L;
    AudioClip music;// 播放音乐
    Label hint,result,notice,banquan;
    TextField canditate;// 输入候选人文本框
    TextField out;// 显示选举结果的文本框
    Button confirm1,cancle,confirm2,refresh,sort;// 分别表示确认、取消、确定、刷新、排序
    Button help;// 使用说明
    Button save;// 保存统计结果
    Checkbox candidate[]=new Checkbox[10];// 选择框数组，代表候选人
    TextField personvote[]=new TextField[10];// 文本条数组，显示每个人的得票情况
    String candidatelist[]=new String[10];// 候选人名单
    int count[]={0,0,0,0,0,0,0,0,0,0};// 记录每个人的得票数
    int totalvote=0;// 总票数
    int peoplenumble=0;// 候选人个数
    int count1=0,invalidatedTicket=0,abstention=0; // 分别表示选的人数，废票数，弃权票数
 public void init(){
   music=getAudioClip(getCodeBase()," 瘦瘦的 - 梁静茹 .mid");
   hint=new Label(" 首先输入候选人的名字（人数不超过 10 ，名字之间用空格分隔） :");
   notice=new Label(" 用下面的选择框统计选票 ( 最多选 3 人 ):");
   result=new Label(" 选举结果 :");
   banquan=new Label("");
   canditate=new TextField(50);
   confirm1=new Button(" 确认 ");cancle=new Button(" 取消 ");confirm2=new Button(" 确定 ");refresh=new Button(" 刷新 ");sort=new Button(" 排序 ");
   confirm2.setEnabled(false);refresh.setEnabled(false);sort.setEnabled(false);
   help=new Button(" 使用说明 ");save=new Button(" 保存结果 ");save.setEnabled(false);
   out=new TextField(50);
   for(int i=0;i<10;i++)
     personvote[i]=new TextField(80);
   Panel p=new Panel();
   Panel p1=new Panel();
   Panel p2=new Panel();
   Panel p3=new Panel();
   Panel p4=new Panel();
   Panel p5=new Panel();
   Panel p6=new Panel();
   Panel p7=new Panel();
   Panel pa=new Panel();
   Panel pb=new Panel();
   Panel pc=new Panel();
   setLayout(new BorderLayout());
   pa.setLayout(new GridLayout(7,1));
   pb.setLayout(new BorderLayout());
   p4.setLayout(new GridLayout(1,5));
   p5.setLayout(new GridLayout(1,5));
   p1.add(hint);p2.add(canditate);p2.add(help);
   p3.add(confirm1);p3.add(cancle);p3.add(notice);
   p4.setBackground(Color.green);
   p5.setBackground(Color.green);
   p6.setBackground(Color.green);
   for(int i=0;i<5;i++){// 创建候选人选项
     candidate[i]=new Checkbox(candidatelist[i]);
     p4.add(candidate[i]);
   }
   for(int i=5;i<10;i++){// 创建候选人选项
     candidate[i]=new Checkbox(candidatelist[i]);
     p5.add(candidate[i]);
   }
   for(int j=0;j<10;j++){  candidate[j].setEnabled(false);
   }
   p6.add(confirm2);p6.add(refresh);p6.add(sort);
   p7.add(result);p7.add(out);p7.add(save);
   pa.add(p1);pa.add(p2);pa.add(p3);pa.add(p4);pa.add(p5);pa.add(p6);pa.add(p7);
   p.setLayout(new GridLayout(10,1));
   for(int i=0;i<10;i++){ 
     p.add(personvote[i]);
   }
   ScrollPane scroll=new ScrollPane();
   scroll.add(p);
   pc.add(banquan);
   pb.add("Center",scroll);
   pb.add("South",pc);
   add("Center",pa);
   add("South",pb);
   confirm1.addActionListener(this);
   cancle.addActionListener(this);
   confirm2.addActionListener(this);
   refresh.addActionListener(this);
   sort.addActionListener(this);
   help.addActionListener(this);
   save.addActionListener(this);
 }// 面板的布局
 public void start(){// 循环播放音乐
   music.loop();}
 public void stop(){// 结束播放
   music.stop();}
 public void actionPerformed(ActionEvent e){// 注册监听
   String s=e.getActionCommand();
   if(s.equals(" 确认 ")){
     confirm1.setEnabled(false);save.setEnabled(true);
     confirm2.setEnabled(true);refresh.setEnabled(true);sort.setEnabled(true);help.setEnabled(true);
     String g=canditate.getText();// 获取输入的候选人
     StringTokenizer st=new StringTokenizer(g);// 字符串分析器
     peoplenumble=st.countTokens();// 统计候选人数
     int i=0;
     while(st.hasMoreTokens()){
       candidatelist[i]=st.nextToken();
       i++;}// 获取语言符号（候选人名单）
     for(int j=0;j<10;j++)
       candidate[j].setLabel(candidatelist[j]);// 将候选人名单添加到复选框里
     for(int j=0;j<peoplenumble;j++)
       candidate[j].setEnabled(true);
     for(int j=peoplenumble;j<10;j++)
       candidate[j].setVisible(false);// 多余的选框设置为不可见
     
   }
   if(s.equals(" 取消 ")){// 重新设置候选人，进行重新投票
     confirm1.setEnabled(true);
     canditate.setText("");
   }
   if(s.equals(" 确定 ")){
     totalvote++;count1=0;
     sort.setEnabled(true);
   for(int j=0;j<10;j++){
     if(candidate[j].getState())
 count1++;}// 统计选了多少人
     if(count1==0) abstention++;// 一个都没选则选票作为弃权
     if(count1>3) invalidatedTicket++;// 选的人数超过 3 个则选票作废
     if(count1<=3&&count1>0){
       for(int j=0;j<peoplenumble;j++)
     if(candidate[j].getState())
 count[j]++;
     }// 统计候选人所得票数
     for(int j=0;j<10;j++)
       candidate[j].setState(false);
     for(int j=0;j<10;j++){
       candidate[j].setState(false);
     }// 清空选框中的勾
     out.setText(" 已经统计了 :"+totalvote+" 张选票，其中弃权票 :"+abstention+" 作废票 :"+invalidatedTicket);// 输出统计结果
     for(int j=0;j<peoplenumble;j++)
       personvote[j].setText(""+candidatelist[j]+" 得票数 :"+count[j]);// 输出各个候选人得票数
     
   }
   if(s.equals(" 刷新 ")){
     confirm1.setEnabled(true);
     confirm2.setEnabled(false);refresh.setEnabled(false);sort.setEnabled(false);save.setEnabled(false);
     totalvote=0;
 peoplenumble=0;
 count1=0;invalidatedTicket=0;abstention=0;
     canditate.setText("");
     out.setText("");
     for(int j=0;j<10;j++){
       candidate[j].setState(false);
     }
     for(int j=peoplenumble;j<10;j++)
       candidate[j].setVisible(true);
     for(int j=0;j<10;j++)
       candidatelist[j]="";
     for(int j=0;j<10;j++)
       count[j]=0;
     for(int j=0;j<10;j++)
       candidate[j].setLabel(candidatelist[j]);
     for(int j=0;j<10;j++)
       personvote[j].setText("");
     
   }
   if(s.equals(" 排序 ")){
     sort.setEnabled(false);
     int m;String n;
     for(int j=0;j<peoplenumble;j++)
       for(int i=j+1;i<peoplenumble;i++)
       if(count[j]<count[i]){
       m=count[j];count[j]=count[i];count[i]=m;
       n=candidatelist[j];candidatelist[j]=candidatelist[i];candidatelist[i]=n;
     }// 按得票数由多到少进行排序
for(int j=0;j<peoplenumble;j++)
       personvote[j].setText(""+candidatelist[j]+" 得票数 :"+count[j]);// 输出排序后各候选人的票数
   }
   if(s.equals(" 使用说明 ")){
     new Help();
   }
   if(s.equals(" 保存结果 ")){
     new Save();
   }
 }
 class Help extends Frame{// “使用说明”的弹出窗体
   /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
Panel p=new Panel();
   TextField help[]=new TextField[6];
   Help(){
   super(" 使用说明 ");
   p.setLayout(new GridLayout(6,1));
   for(int i=0;i<6;i++)
     help[i]=new TextField(10);
   for(int i=0;i<6;i++){ 
     p.add(help[i]);
   }
   ScrollPane scroll=new ScrollPane();
   scroll.add(p);
   add(scroll);
   help[0].setText(" 使用说明 :");
 help[1].setText("1: 在文本框中输入候选人名单，点击“确认”以完成候选人的设置，点击“取消”可以重新设置候选人。 ");
 help[2].setText("2: 对候选人进行投票，点击下面的“确定”以确认选票。 ( 注意：每点一次确定将产生一张选票！ )");
 help[3].setText("3: 确定选票后，会自动统计结果，点击“排序”可以对候选人所得的票数由高到低进行排序。 ");
 help[4].setText("4: 点击“刷新”可以重新设置候选人，并开始新的一轮投票 ");
 help[5].setText("5: 在任何时候可以点击“使用说明”来查看帮助，点击“保存结果”，可以将统计以文本的形式显示出来。 ");
   setSize(600,200);
   setVisible(true);
   addWindowListener(new closeWin());
   }
   class closeWin extends WindowAdapter{
     public void windowClosing(WindowEvent e){
       Window w=e.getWindow();
       w.dispose();
     }
   }
 }
 class Save extends Frame{/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
// “保存结果”的弹出窗体
   TextArea save;
   Save(){
     super(" 统计结果 ");
     save=new TextArea(11,1);
     add(save);
     save.setText(out.getText()+'\n'+personvote[0].getText()+'\n'+personvote[1].getText()+'\n'+
       personvote[2].getText()+'\n'+personvote[3].getText()+'\n'
       +personvote[4].getText()+'\n'+personvote[5].getText()+'\n'
       +personvote[6].getText()+'\n'+personvote[7].getText()+'\n'
       +personvote[8].getText()+'\n'+personvote[9].getText()+'\n');
     setSize(300,300);
     setVisible(true);
     addWindowListener(new closeWin());
   }
   class closeWin extends WindowAdapter{
     public void windowClosing(WindowEvent e){
       Window w=e.getWindow();
       w.dispose();
     }
   }
   }
}

