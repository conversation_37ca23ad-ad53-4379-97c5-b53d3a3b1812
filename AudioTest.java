import java.applet.AudioClip;
import java.io.File;

public class AudioTest {
    public static void main(String[] args) {
        System.out.println("音频文件测试");
        System.out.println("=============");
        
        // 检查文件是否存在
        File wavFile = new File("background.wav");
        File mp3File = new File("zhoumusic.mp3");
        
        System.out.println("background.wav 存在: " + wavFile.exists());
        if (wavFile.exists()) {
            System.out.println("background.wav 大小: " + wavFile.length() + " 字节");
        }
        
        System.out.println("zhoumusic.mp3 存在: " + mp3File.exists());
        if (mp3File.exists()) {
            System.out.println("zhoumusic.mp3 大小: " + mp3File.length() + " 字节");
        }
        
        // 尝试加载音频
        if (wavFile.exists()) {
            try {
                AudioClip clip = java.applet.Applet.newAudioClip(wavFile.toURI().toURL());
                System.out.println("✅ 成功创建AudioClip对象");
                
                // 尝试播放
                clip.play();
                System.out.println("✅ 音频播放命令已发送");
                
                // 等待一下
                Thread.sleep(2000);
                
                clip.stop();
                System.out.println("✅ 音频停止");
                
            } catch (Exception e) {
                System.out.println("❌ 音频加载/播放失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
}
