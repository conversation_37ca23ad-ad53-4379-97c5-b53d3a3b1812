@echo off
title Voting System Launcher
chcp 936 >nul

echo Starting Voting Management System...
echo.

REM Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Java not found! Please install Java 8+
    echo Download: https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

REM Start with proper encoding
java -Dfile.encoding=GBK -Dsun.jnu.encoding=GBK -Duser.language=zh -Duser.country=CN -jar "投票管理系统-修复版.jar"

pause
