@echo off
chcp 65001 >nul
echo 转换 zhoumusic.mp3 为背景音乐
echo ================================
echo.

if not exist "zhoumusic.mp3" (
    echo 错误：未找到 zhoumusic.mp3 文件！
    echo 请确保文件在当前目录下
    pause
    exit /b 1
)

echo 找到音频文件：zhoumusic.mp3
echo 正在尝试转换为WAV格式...
echo.

REM 检查是否有ffmpeg
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用ffmpeg转换...
    ffmpeg -i zhoumusic.mp3 -acodec pcm_s16le -ar 44100 -ac 2 background.wav -y
    if %errorlevel% equ 0 (
        echo.
        echo ✅ 转换成功！已生成 background.wav
        echo 现在启动程序将会播放背景音乐
        goto :test
    ) else (
        echo ❌ ffmpeg转换失败
        goto :manual
    )
) else (
    echo 未检测到ffmpeg，需要手动转换
    goto :manual
)

:manual
echo.
echo 📋 手动转换步骤：
echo ================
echo.
echo 方法1：在线转换（推荐）
echo 1. 打开浏览器访问：https://convertio.co/mp3-wav/
echo 2. 上传 zhoumusic.mp3 文件
echo 3. 点击转换按钮
echo 4. 下载转换后的WAV文件
echo 5. 将下载的文件重命名为：background.wav
echo 6. 放在当前目录下
echo.
echo 方法2：使用Audacity（免费软件）
echo 1. 下载Audacity：https://www.audacityteam.org/
echo 2. 安装后打开，导入 zhoumusic.mp3
echo 3. 选择：文件 → 导出 → 导出为WAV
echo 4. 保存为 background.wav
echo.
echo 方法3：使用格式工厂
echo 1. 下载安装格式工厂
echo 2. 选择音频转换 → WAV
echo 3. 添加 zhoumusic.mp3，转换为WAV
echo 4. 重命名为 background.wav
echo.
goto :end

:test
echo.
echo 🎵 测试背景音乐
echo ===============
echo 现在启动投票系统测试音乐...
echo.
"D:\javaJDK8\bin\java" -cp . keshe.VotingSystemMain
echo.

:end
echo 按任意键退出...
pause >nul
