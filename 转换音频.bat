@echo off
echo 音频格式转换工具
echo ================
echo.

echo 请将你的MP3文件重命名为 "input.mp3" 并放在当前目录
echo 然后按任意键继续...
pause

if not exist "input.mp3" (
    echo 错误：未找到 input.mp3 文件！
    echo 请将你的MP3文件重命名为 input.mp3 并放在当前目录
    pause
    exit /b 1
)

echo 正在转换 input.mp3 为 background.wav...
echo.

REM 检查是否安装了ffmpeg
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 未检测到ffmpeg，请使用以下方法之一：
    echo.
    echo 方法1：在线转换
    echo 访问：https://convertio.co/mp3-wav/
    echo.
    echo 方法2：安装ffmpeg
    echo 1. 下载ffmpeg：https://ffmpeg.org/download.html
    echo 2. 解压到C:\ffmpeg
    echo 3. 将C:\ffmpeg\bin添加到系统PATH环境变量
    echo.
    echo 方法3：使用Audacity软件
    echo 下载：https://www.audacityteam.org/
    echo.
    pause
    exit /b 1
)

REM 使用ffmpeg转换
ffmpeg -i input.mp3 -acodec pcm_s16le -ar 44100 background.wav

if %errorlevel% equ 0 (
    echo.
    echo 转换成功！已生成 background.wav
    echo 现在可以运行投票系统，将会播放背景音乐
    echo.
) else (
    echo.
    echo 转换失败！请检查input.mp3文件是否有效
    echo.
)

pause
