import java.util.StringTokenizer;

/**
 * 简单的功能测试
 */
public class SimpleTest {
    public static void main(String[] args) {
        System.out.println("=== 投票管理系统功能测试 ===");
        
        // 测试字符串解析
        System.out.println("\n1. 测试候选人名单解析:");
        String input = "张三 李四 王五 赵六";
        StringTokenizer st = new StringTokenizer(input);
        System.out.println("输入: " + input);
        System.out.println("候选人数量: " + st.countTokens());
        
        st = new StringTokenizer(input);
        int i = 1;
        while(st.hasMoreTokens()) {
            System.out.println("  " + i + ". " + st.nextToken());
            i++;
        }
        
        // 测试排序算法
        System.out.println("\n2. 测试排序算法:");
        String[] candidates = {"张三", "李四", "王五", "赵六"};
        int[] votes = {5, 3, 8, 2};
        
        System.out.println("排序前:");
        for(int j = 0; j < candidates.length; j++) {
            System.out.println("  " + candidates[j] + ": " + votes[j] + "票");
        }
        
        // 冒泡排序
        for(int j = 0; j < candidates.length - 1; j++) {
            for(int k = j + 1; k < candidates.length; k++) {
                if(votes[j] < votes[k]) {
                    int tempVotes = votes[j];
                    votes[j] = votes[k];
                    votes[k] = tempVotes;
                    
                    String tempName = candidates[j];
                    candidates[j] = candidates[k];
                    candidates[k] = tempName;
                }
            }
        }
        
        System.out.println("排序后:");
        for(int j = 0; j < candidates.length; j++) {
            System.out.println("  " + (j+1) + ". " + candidates[j] + ": " + votes[j] + "票");
        }
        
        System.out.println("\n✓ 基本功能测试通过！");
        System.out.println("\n现在可以运行图形界面程序:");
        System.out.println("java keshe.VotingSystemMain");
    }
}
