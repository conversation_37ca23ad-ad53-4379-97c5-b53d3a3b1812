<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>投票管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .description {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e8f4f8;
            border-radius: 5px;
        }
        .applet-container {
            text-align: center;
            margin: 20px 0;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>投票管理系统</h1>
        
        <div class="description">
            <h3>系统功能介绍：</h3>
            <ul>
                <li>支持自定义候选人（最多10个）</li>
                <li>支持多次投票，每次最多选择3人</li>
                <li>自动统计总票数、有效票、废票、弃权票</li>
                <li>支持按得票数排序</li>
                <li>提供使用说明和结果保存功能</li>
                <li>背景音乐播放（如果音频文件存在）</li>
            </ul>
        </div>
        
        <div class="applet-container">
            <applet code="keshe.VotingSystem.class" width="800" height="600">
                <param name="bgcolor" value="white">
                您的浏览器不支持Java Applet，请使用支持Java的浏览器或运行独立应用程序。
            </applet>
        </div>
        
        <div class="note">
            <h4>使用说明：</h4>
            <ol>
                <li>在文本框中输入候选人名单，候选人之间用空格分隔</li>
                <li>点击"确认"按钮设置候选人</li>
                <li>选择要投票的候选人（最多3个），点击"确定"提交选票</li>
                <li>可以多次投票，系统会自动统计结果</li>
                <li>点击"排序"按钮可以按得票数排序</li>
                <li>点击"保存结果"可以查看详细统计信息</li>
                <li>点击"刷新"可以重新开始新的投票</li>
            </ol>
        </div>
        
        <div class="note">
            <h4>注意事项：</h4>
            <ul>
                <li>如果一张选票选择超过3个候选人，该票将被视为废票</li>
                <li>如果一张选票没有选择任何候选人，该票将被视为弃权票</li>
                <li>只有选择1-3个候选人的选票才是有效票</li>
                <li>如果需要运行独立应用程序，请编译并运行VotingSystemMain.java</li>
            </ul>
        </div>
    </div>
</body>
</html>
