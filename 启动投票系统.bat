@echo off
chcp 65001 >nul
echo 正在启动投票管理系统...
echo.

REM 设置Java路径
set JAVA_HOME=D:\javaJDK8
set PATH=%JAVA_HOME%\bin;%PATH%

REM 检查Java是否可用
"%JAVA_HOME%\bin\java" -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Java环境未找到，尝试使用系统Java...
    java -version >nul 2>&1
    if %errorlevel% neq 0 (
        echo 错误：找不到Java环境！
        echo 请确保Java已正确安装并配置环境变量。
        pause
        exit /b 1
    )
    set JAVA_CMD=java
    set JAVAC_CMD=javac
) else (
    set JAVA_CMD="%JAVA_HOME%\bin\java"
    set JAVAC_CMD="%JAVA_HOME%\bin\javac"
)

REM 检查是否已编译
if not exist "keshe\VotingSystemMain.class" (
    echo 正在编译程序...
    %JAVAC_CMD% -encoding UTF-8 -d . VotingSystem.java VotingSystemMain.java
    if %errorlevel% neq 0 (
        echo 编译失败！
        pause
        exit /b 1
    )
    echo 编译完成！
)

echo 启动图形界面...
echo 如果程序启动成功，将会打开一个投票系统窗口
echo.
%JAVA_CMD% -cp . keshe.VotingSystemMain

echo.
echo 程序已关闭。
pause
