@echo off
echo 投票管理系统启动器
echo ===================
echo.

REM 直接使用完整路径的Java
echo 正在启动程序...
D:\javaJDK8\bin\java -cp . keshe.VotingSystemMain

if %errorlevel% neq 0 (
    echo.
    echo 启动失败，尝试重新编译...
    D:\javaJDK8\bin\javac -encoding UTF-8 -d . VotingSystem.java VotingSystemMain.java
    if %errorlevel% equ 0 (
        echo 编译成功，重新启动...
        D:\javaJDK8\bin\java -cp . keshe.VotingSystemMain
    ) else (
        echo 编译失败！
    )
)

echo.
echo 按任意键退出...
pause >nul
